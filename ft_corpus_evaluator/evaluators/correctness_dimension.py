import re
from typing import List, Dict, Any
from .dimension_framework import BaseDimension, BaseMetric
from ..models.evaluation_models import EvaluationDimension, EvaluationLevel, MetricResult
from ..models.corpus_model import FTCorpus


class FormatCorrectnessMetric(BaseMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("format_correctness", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        format_issues = []
        score = 100
        
        rdc_id_pattern = self.config.get('rdc_id_pattern', r'^RAN-\d+$')
        if not re.match(rdc_id_pattern, corpus.rdc_info.rdc_id or ''):
            format_issues.append("Invalid RDC ID format")
            score -= 25
        
        gerrit_pattern = self.config.get('gerrit_link_pattern', 
                                       r'^https://gerrit\.zte\.com\.cn/#/c/\d+(/\d+)?$')
        if not re.match(gerrit_pattern, corpus.rdc_info.gerrit_link or ''):
            format_issues.append("Invalid Gerrit link format")
            score -= 25
        
        date_pattern = self.config.get('date_pattern', r'^\d{4}-\d{2}-\d{2}$')
        if not re.match(date_pattern, corpus.rdc_info.date or ''):
            format_issues.append("Invalid date format (expected: YYYY-MM-DD)")
            score -= 20
        
        if corpus.rdc_info.repo_name and not corpus.rdc_info.repo_name.strip():
            format_issues.append("Repository name is empty")
            score -= 15
        
        invalid_tags = []
        for tag in corpus.tag_identification.business_content_scene_tags:
            if not tag or not tag.strip():
                invalid_tags.append("empty business tag")
        for tag in corpus.tag_identification.code_modify_scene_tags:
            if not tag or not tag.strip():
                invalid_tags.append("empty code modify tag")
        
        if invalid_tags:
            format_issues.append(f"Invalid tags: {len(invalid_tags)} empty tags")
            score -= min(15, len(invalid_tags) * 3)
        
        score = max(0, score)
        
        if score < 60:
            level = EvaluationLevel.CRITICAL
            message = f"Critical format errors: {len(format_issues)}"
        elif score < 80:
            level = EvaluationLevel.WARNING
            message = f"Format issues found: {len(format_issues)}"
        else:
            level = EvaluationLevel.INFO
            message = "Format validation passed"
        
        suggestions = [f"Fix format issue: {issue}" for issue in format_issues]
        
        return self.create_result(
            score=score,
            level=level,
            message=message,
            dimension=EvaluationDimension.CORRECTNESS,
            details={
                "format_issues": format_issues,
                "invalid_tags": invalid_tags,
                "issues_count": len(format_issues)
            },
            suggestions=suggestions
        )


class LogicalConsistencyMetric(BaseMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("logical_consistency", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        consistency_issues = []
        score = 100
        
        steps = corpus.test_info.tc_steps
        expected_results = corpus.test_info.tc_expected_results
        
        if len(steps) != len(expected_results):
            consistency_issues.append(
                f"Steps-results mismatch: {len(steps)} steps vs {len(expected_results)} results"
            )
            score -= 30
        
        for i, step in enumerate(steps):
            if i < len(expected_results):
                expected = expected_results[i]
                if self._has_action_result_mismatch(step, expected):
                    consistency_issues.append(f"Step {i+1}: action-result mismatch")
                    score -= 10
        
        title_keywords = self._extract_keywords(corpus.test_info.test_title)
        steps_keywords = []
        for step in steps:
            steps_keywords.extend(self._extract_keywords(step))
        
        if not any(keyword in steps_keywords for keyword in title_keywords):
            consistency_issues.append("Test title doesn't match test content")
            score -= 20
        
        business_tags = corpus.tag_identification.business_content_scene_tags
        code_tags = corpus.tag_identification.code_modify_scene_tags
        
        if business_tags and code_tags:
            if not self._tags_are_consistent(business_tags, code_tags):
                consistency_issues.append("Business and code tags are inconsistent")
                score -= 15
        
        if corpus.code_snippets:
            code_languages = set(snippet.language for snippet in corpus.code_snippets)
            if len(code_languages) > 3:
                consistency_issues.append("Too many different programming languages")
                score -= 10
        
        score = max(0, score)
        
        if score < 60:
            level = EvaluationLevel.CRITICAL
            message = "Critical logical inconsistencies found"
        elif score < 80:
            level = EvaluationLevel.WARNING
            message = "Logical inconsistencies detected"
        else:
            level = EvaluationLevel.INFO
            message = "Content is logically consistent"
        
        suggestions = [f"Fix: {issue}" for issue in consistency_issues]
        
        return self.create_result(
            score=score,
            level=level,
            message=message,
            dimension=EvaluationDimension.CORRECTNESS,
            details={
                "consistency_issues": consistency_issues,
                "issues_count": len(consistency_issues),
                "steps_results_ratio": f"{len(steps)}:{len(expected_results)}"
            },
            suggestions=suggestions
        )
    
    def _has_action_result_mismatch(self, step: str, expected: str) -> bool:
        action_keywords = ['执行', '运行', '启动', '配置', '设置', '发送', '接收']
        result_keywords = ['成功', '失败', '显示', '输出', '返回', '收到']
        
        step_has_action = any(keyword in step for keyword in action_keywords)
        expected_has_result = any(keyword in expected for keyword in result_keywords)
        
        return step_has_action and not expected_has_result
    
    def _extract_keywords(self, text: str) -> List[str]:
        keywords = re.findall(r'[A-Za-z]+|[\u4e00-\u9fff]+', text)
        return [kw for kw in keywords if len(kw) > 1]
    
    def _tags_are_consistent(self, business_tags: List[str], code_tags: List[str]) -> bool:
        business_keywords = set()
        for tag in business_tags:
            business_keywords.update(self._extract_keywords(tag))
        
        code_keywords = set()
        for tag in code_tags:
            code_keywords.update(self._extract_keywords(tag))
        
        return len(business_keywords.intersection(code_keywords)) > 0


class DataValidityMetric(BaseMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("data_validity", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        validity_issues = []
        score = 100
        
        if corpus.rdc_info.rdc_id:
            rdc_number = re.search(r'RAN-(\d+)', corpus.rdc_info.rdc_id)
            if rdc_number and int(rdc_number.group(1)) < 1000000:
                validity_issues.append("RDC ID number seems too low")
                score -= 10
        
        if corpus.rdc_info.date:
            try:
                year = int(corpus.rdc_info.date.split('-')[0])
                if year < 2020 or year > 2030:
                    validity_issues.append("Date year is out of reasonable range")
                    score -= 15
            except (ValueError, IndexError):
                validity_issues.append("Date format is invalid")
                score -= 20
        
        for i, step in enumerate(corpus.test_info.tc_steps):
            if len(step.strip()) < 5:
                validity_issues.append(f"Step {i+1} is too short")
                score -= 5
            elif len(step) > 1000:
                validity_issues.append(f"Step {i+1} is excessively long")
                score -= 5
        
        for i, result in enumerate(corpus.test_info.tc_expected_results):
            if len(result.strip()) < 3:
                validity_issues.append(f"Expected result {i+1} is too short")
                score -= 5
        
        for i, snippet in enumerate(corpus.code_snippets):
            if snippet.language == "unknown" and len(snippet.content) > 20:
                validity_issues.append(f"Code snippet {i+1} has unknown language")
                score -= 5
            
            if not snippet.file_path and len(snippet.content) > 50:
                validity_issues.append(f"Code snippet {i+1} missing file path")
                score -= 5
        
        duplicate_steps = self._find_duplicate_steps(corpus.test_info.tc_steps)
        if duplicate_steps:
            validity_issues.append(f"Duplicate test steps found: {len(duplicate_steps)}")
            score -= len(duplicate_steps) * 10
        
        score = max(0, score)
        
        if score < 60:
            level = EvaluationLevel.CRITICAL
            message = "Critical data validity issues"
        elif score < 80:
            level = EvaluationLevel.WARNING
            message = "Data validity issues detected"
        else:
            level = EvaluationLevel.INFO
            message = "Data is valid"
        
        suggestions = [f"Fix: {issue}" for issue in validity_issues[:5]]
        
        return self.create_result(
            score=score,
            level=level,
            message=message,
            dimension=EvaluationDimension.CORRECTNESS,
            details={
                "validity_issues": validity_issues,
                "duplicate_steps": duplicate_steps,
                "issues_count": len(validity_issues)
            },
            suggestions=suggestions
        )
    
    def _find_duplicate_steps(self, steps: List[str]) -> List[int]:
        seen = {}
        duplicates = []
        for i, step in enumerate(steps):
            normalized = step.strip().lower()
            if normalized in seen:
                duplicates.append(i)
            else:
                seen[normalized] = i
        return duplicates


class CorrectnessDimension(BaseDimension):
    def __init__(self):
        super().__init__(EvaluationDimension.CORRECTNESS)
        self.register_metric(FormatCorrectnessMetric())
        self.register_metric(LogicalConsistencyMetric())
        self.register_metric(DataValidityMetric())

from typing import List, Dict, Any
from .dimension_framework import BaseDimension, BaseMetric
from ..models.evaluation_models import EvaluationDimension, EvaluationLevel, MetricResult
from ..models.corpus_model import FTCorpus


class RequiredFieldsMetric(BaseMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("required_fields", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        required_fields = self.config.get('required_fields', {
            'rdc_info': ['rdc_id', 'repo_name', 'gerrit_link', 'date'],
            'test_info': ['test_title', 'tc_steps', 'tc_expected_results'],
            'tag_identification': ['business_content_scene_tags', 'code_modify_scene_tags']
        })
        
        missing_fields = []
        total_fields = 0
        present_fields = 0
        
        for section, fields in required_fields.items():
            section_obj = getattr(corpus, section, None)
            if section_obj is None:
                missing_fields.extend([f"{section}.{field}" for field in fields])
                total_fields += len(fields)
                continue
            
            for field in fields:
                total_fields += 1
                field_value = getattr(section_obj, field, None)
                if field_value is not None and field_value != "" and field_value != []:
                    present_fields += 1
                else:
                    missing_fields.append(f"{section}.{field}")
        
        score = (present_fields / total_fields * 100) if total_fields > 0 else 0
        
        if score < 60:
            level = EvaluationLevel.CRITICAL
            message = f"Critical: {len(missing_fields)} required fields missing"
        elif score < 80:
            level = EvaluationLevel.WARNING
            message = f"Warning: {len(missing_fields)} required fields missing"
        else:
            level = EvaluationLevel.INFO
            message = "All required fields are present"
        
        suggestions = []
        if missing_fields:
            suggestions = [f"Add missing field: {field}" for field in missing_fields[:5]]
            if len(missing_fields) > 5:
                suggestions.append(f"... and {len(missing_fields) - 5} more fields")
        
        return self.create_result(
            score=score,
            level=level,
            message=message,
            dimension=EvaluationDimension.COMPLETENESS,
            details={
                "missing_fields": missing_fields,
                "present_fields": present_fields,
                "total_fields": total_fields,
                "completion_rate": f"{present_fields}/{total_fields}"
            },
            suggestions=suggestions
        )


class ContentStructureMetric(BaseMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("content_structure", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        structure_issues = []
        score = 100
        
        if not corpus.test_info.test_title or len(corpus.test_info.test_title.strip()) < 5:
            structure_issues.append("Test title is too short or missing")
            score -= 20
        
        if not corpus.test_info.tc_steps or len(corpus.test_info.tc_steps) < 2:
            structure_issues.append("Insufficient test steps (minimum 2 required)")
            score -= 25
        
        if not corpus.test_info.tc_expected_results:
            structure_issues.append("Expected results are missing")
            score -= 25
        
        if not corpus.tag_identification.business_content_scene_tags:
            structure_issues.append("Business content scene tags are missing")
            score -= 15
        
        if not corpus.tag_identification.code_modify_scene_tags:
            structure_issues.append("Code modify scene tags are missing")
            score -= 15
        
        score = max(0, score)
        
        if score < 60:
            level = EvaluationLevel.CRITICAL
            message = f"Critical structure issues found: {len(structure_issues)}"
        elif score < 80:
            level = EvaluationLevel.WARNING
            message = f"Structure issues found: {len(structure_issues)}"
        else:
            level = EvaluationLevel.INFO
            message = "Content structure is complete"
        
        suggestions = [f"Fix: {issue}" for issue in structure_issues]
        
        return self.create_result(
            score=score,
            level=level,
            message=message,
            dimension=EvaluationDimension.COMPLETENESS,
            details={
                "structure_issues": structure_issues,
                "issues_count": len(structure_issues)
            },
            suggestions=suggestions
        )


class TestStepsCompletenessMetric(BaseMetric):
    def __init__(self, weight: float = 1.0, enabled: bool = True):
        super().__init__("test_steps_completeness", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        min_step_length = self.config.get('min_step_length', 10)
        min_steps_count = self.config.get('min_steps_count', 2)
        
        steps = corpus.test_info.tc_steps
        expected_results = corpus.test_info.tc_expected_results
        
        issues = []
        score = 100
        
        if len(steps) < min_steps_count:
            issues.append(f"Too few test steps: {len(steps)} (minimum: {min_steps_count})")
            score -= 30
        
        short_steps = [i for i, step in enumerate(steps) if len(step.strip()) < min_step_length]
        if short_steps:
            issues.append(f"Steps too short: {len(short_steps)} steps")
            score -= 20
        
        if len(expected_results) != len(steps):
            issues.append(f"Mismatch: {len(steps)} steps vs {len(expected_results)} expected results")
            score -= 25
        
        empty_steps = [i for i, step in enumerate(steps) if not step.strip()]
        if empty_steps:
            issues.append(f"Empty steps found: {len(empty_steps)}")
            score -= 25
        
        score = max(0, score)
        
        if score < 60:
            level = EvaluationLevel.CRITICAL
            message = "Test steps are incomplete"
        elif score < 80:
            level = EvaluationLevel.WARNING
            message = "Test steps need improvement"
        else:
            level = EvaluationLevel.INFO
            message = "Test steps are complete"
        
        suggestions = []
        if len(steps) < min_steps_count:
            suggestions.append(f"Add more test steps (current: {len(steps)}, minimum: {min_steps_count})")
        if short_steps:
            suggestions.append("Expand short test steps with more details")
        if len(expected_results) != len(steps):
            suggestions.append("Ensure each test step has corresponding expected result")
        
        return self.create_result(
            score=score,
            level=level,
            message=message,
            dimension=EvaluationDimension.COMPLETENESS,
            details={
                "steps_count": len(steps),
                "expected_results_count": len(expected_results),
                "short_steps": short_steps,
                "empty_steps": empty_steps,
                "issues": issues
            },
            suggestions=suggestions
        )


class CodeSnippetsCompletenessMetric(BaseMetric):
    def __init__(self, weight: float = 0.8, enabled: bool = True):
        super().__init__("code_snippets_completeness", weight, enabled)
    
    def evaluate(self, corpus: FTCorpus) -> MetricResult:
        min_snippets = self.config.get('min_snippets', 1)
        min_snippet_length = self.config.get('min_snippet_length', 50)
        
        snippets = corpus.code_snippets
        issues = []
        score = 100
        
        if len(snippets) < min_snippets:
            issues.append(f"Too few code snippets: {len(snippets)} (minimum: {min_snippets})")
            score -= 40
        
        incomplete_snippets = []
        for i, snippet in enumerate(snippets):
            snippet_issues = []
            if not snippet.file_path or not snippet.file_path.strip():
                snippet_issues.append("missing file path")
            if not snippet.language or snippet.language == "unknown":
                snippet_issues.append("missing or unknown language")
            if len(snippet.content) < min_snippet_length:
                snippet_issues.append("content too short")
            
            if snippet_issues:
                incomplete_snippets.append({
                    "index": i,
                    "issues": snippet_issues
                })
        
        if incomplete_snippets:
            issues.append(f"Incomplete code snippets: {len(incomplete_snippets)}")
            score -= min(30, len(incomplete_snippets) * 10)
        
        score = max(0, score)
        
        if len(snippets) == 0:
            level = EvaluationLevel.WARNING
            message = "No code snippets provided"
        elif score < 60:
            level = EvaluationLevel.CRITICAL
            message = "Code snippets are incomplete"
        elif score < 80:
            level = EvaluationLevel.WARNING
            message = "Code snippets need improvement"
        else:
            level = EvaluationLevel.INFO
            message = "Code snippets are complete"
        
        suggestions = []
        if len(snippets) < min_snippets:
            suggestions.append(f"Add more code snippets (current: {len(snippets)}, minimum: {min_snippets})")
        for snippet_info in incomplete_snippets[:3]:
            suggestions.append(f"Fix snippet {snippet_info['index']}: {', '.join(snippet_info['issues'])}")
        
        return self.create_result(
            score=score,
            level=level,
            message=message,
            dimension=EvaluationDimension.COMPLETENESS,
            details={
                "snippets_count": len(snippets),
                "incomplete_snippets": incomplete_snippets,
                "issues": issues
            },
            suggestions=suggestions
        )


class CompletenessDimension(BaseDimension):
    def __init__(self):
        super().__init__(EvaluationDimension.COMPLETENESS)
        self.register_metric(RequiredFieldsMetric())
        self.register_metric(ContentStructureMetric())
        self.register_metric(TestStepsCompletenessMetric())
        self.register_metric(CodeSnippetsCompletenessMetric())

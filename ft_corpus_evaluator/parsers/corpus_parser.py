import re
from pathlib import Path
from typing import List, Dict, Any
from ..models.corpus_model import FTCorpus, RdcInfo, TestInfo, TagIdentification, CodeSnippet

class CorpusParser:
    def __init__(self):
        self.section_patterns = {
            'rdc_info': r'\*\*RdcInfo\*\*(.*?)(?=\n##|\n\*\*|$)',
            'test_info': r'## TestInfo(.*?)(?=\n##|\n\*\*|$)',
            'tag_identification': r'## Tag Identification(.*?)(?=\n##|\n\*\*|$)',
            'code_blocks': r'```(\w+)?\n(.*?)\n```'
        }
    
    def parse_file(self, file_path: Path) -> FTCorpus:
        content = file_path.read_text(encoding='utf-8')
        
        rdc_info = self._parse_rdc_info(content)
        test_info = self._parse_test_info(content)
        tag_identification = self._parse_tag_identification(content)
        code_snippets = self._parse_code_snippets(content)
        
        return FTCorpus(
            file_path=str(file_path),
            rdc_info=rdc_info,
            test_info=test_info,
            tag_identification=tag_identification,
            code_snippets=code_snippets,
            raw_content=content
        )
    
    def _parse_rdc_info(self, content: str) -> RdcInfo:
        rdc_match = re.search(self.section_patterns['rdc_info'], content, re.DOTALL)
        if not rdc_match:
            return RdcInfo("", "", "", "")
        
        rdc_content = rdc_match.group(1)
        
        rdc_id = self._extract_field(rdc_content, r'- rdc_id:(.+)')
        repo_name = self._extract_field(rdc_content, r'- repo_name:(.+)')
        gerrit_link = self._extract_field(rdc_content, r'- gerrit_link:(.+)')
        date = self._extract_field(rdc_content, r'- date:(.+)')
        compile_script = self._extract_field(rdc_content, r'- compile_script:(.+)')
        compile_command_json_path = self._extract_field(rdc_content, r'- compile_command_json_path:(.+)')
        
        return RdcInfo(
            rdc_id=rdc_id,
            repo_name=repo_name,
            gerrit_link=gerrit_link,
            date=date,
            compile_script=compile_script,
            compile_command_json_path=compile_command_json_path
        )
    
    def _parse_test_info(self, content: str) -> TestInfo:
        test_match = re.search(self.section_patterns['test_info'], content, re.DOTALL)
        if not test_match:
            return TestInfo("")
        
        test_content = test_match.group(1)
        
        test_title = self._extract_field(test_content, r'### 测试标题[：:]\s*(.+)')
        preconditions = self._extract_list_field(test_content, r'### 预制条件[：:]?(.*?)(?=###|$)')
        tc_steps = self._extract_list_field(test_content, r'### TC步骤[：:]?(.*?)(?=###|$)')
        tc_expected_results = self._extract_list_field(test_content, r'### TC预期结果[：:]?(.*?)(?=###|$)')
        expected_results = self._extract_field(test_content, r'### 预期结果[：:]\s*(.+)')
        pass_criteria = self._extract_field(test_content, r'### 通过准则[：:]\s*(.+)')
        
        return TestInfo(
            test_title=test_title,
            preconditions=preconditions,
            tc_steps=tc_steps,
            tc_expected_results=tc_expected_results,
            expected_results=expected_results,
            pass_criteria=pass_criteria
        )
    
    def _parse_tag_identification(self, content: str) -> TagIdentification:
        tag_match = re.search(self.section_patterns['tag_identification'], content, re.DOTALL)
        if not tag_match:
            return TagIdentification()
        
        tag_content = tag_match.group(1)
        
        business_tags = self._extract_list_field(tag_content, r'### business_content_scence_tag(.*?)(?=###|$)')
        code_tags = self._extract_list_field(tag_content, r'### code_modify_scence_tag(.*?)(?=###|$)')
        
        return TagIdentification(
            business_content_scene_tags=business_tags,
            code_modify_scene_tags=code_tags
        )
    
    def _parse_code_snippets(self, content: str) -> List[CodeSnippet]:
        code_blocks = re.findall(self.section_patterns['code_blocks'], content, re.DOTALL)
        snippets = []
        
        for language, code in code_blocks:
            file_path_match = re.search(r'代码路径[：:]?\s*(.+)', content)
            file_path = file_path_match.group(1) if file_path_match else ""
            
            snippets.append(CodeSnippet(
                file_path=file_path,
                language=language or "unknown",
                content=code.strip()
            ))
        
        return snippets
    
    def _extract_field(self, content: str, pattern: str) -> str:
        match = re.search(pattern, content, re.MULTILINE)
        return match.group(1).strip() if match else ""
    
    def _extract_list_field(self, content: str, pattern: str) -> List[str]:
        match = re.search(pattern, content, re.DOTALL)
        if not match:
            return []
        
        text = match.group(1).strip()
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        return [re.sub(r'^[-*]\s*', '', line) for line in lines if line]
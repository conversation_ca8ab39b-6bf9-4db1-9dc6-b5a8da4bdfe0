from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
from datetime import datetime

@dataclass
class RdcInfo:
    rdc_id: str
    repo_name: str
    gerrit_link: str
    date: str
    compile_script: Optional[str] = None
    compile_command_json_path: Optional[str] = None

@dataclass
class TestInfo:
    test_title: str
    preconditions: List[str] = field(default_factory=list)
    tc_steps: List[str] = field(default_factory=list)
    tc_expected_results: List[str] = field(default_factory=list)
    expected_results: str = ""
    pass_criteria: str = ""

@dataclass
class TagIdentification:
    business_content_scene_tags: List[str] = field(default_factory=list)
    code_modify_scene_tags: List[str] = field(default_factory=list)

@dataclass
class CodeSnippet:
    file_path: str
    language: str
    content: str
    description: Optional[str] = None

@dataclass
class FTCorpus:
    file_path: str
    rdc_info: RdcInfo
    test_info: TestInfo
    tag_identification: TagIdentification
    code_snippets: List[CodeSnippet] = field(default_factory=list)
    similar_cases: List[Dict[str, Any]] = field(default_factory=list)
    raw_content: str = ""
    parsed_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'file_path': self.file_path,
            'rdc_info': self.rdc_info.__dict__,
            'test_info': self.test_info.__dict__,
            'tag_identification': self.tag_identification.__dict__,
            'code_snippets': [snippet.__dict__ for snippet in self.code_snippets],
            'similar_cases': self.similar_cases,
            'parsed_at': self.parsed_at.isoformat()
        }
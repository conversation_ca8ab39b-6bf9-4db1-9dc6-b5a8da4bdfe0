from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
from datetime import datetime

@dataclass
class RdcInfo:
    rdc_id: str
    repo_name: str
    gerrit_link: str
    date: str
    final_test: Optional[str] = None
    compile_script: Optional[str] = None
    compile_command_json_path: Optional[str] = None

@dataclass
class TestInfo:
    test_title: str
    preconditions: List[str] = field(default_factory=list)
    tc_steps: List[str] = field(default_factory=list)
    tc_expected_results: List[str] = field(default_factory=list)
    expected_results: str = ""
    pass_criteria: str = ""

@dataclass
class TagIdentification:
    business_content_scene_tags: List[str] = field(default_factory=list)
    code_modify_scene_tags: List[str] = field(default_factory=list)

@dataclass
class CodeSnippet:
    file_path: str
    language: str
    content: str
    description: Optional[str] = None

@dataclass
class DependencyCode:
    file_path: str
    language: str
    content: str
    description: Optional[str] = None

@dataclass
class SimilarTestCase:
    test_title: str
    preconditions: List[str] = field(default_factory=list)
    tc_steps: List[str] = field(default_factory=list)
    tc_expected_results: List[str] = field(default_factory=list)
    expected_results: str = ""
    pass_criteria: str = ""

@dataclass
class SimilarCodeInfo:
    similar_test_cases: List[SimilarTestCase] = field(default_factory=list)
    similar_ft_code: List[CodeSnippet] = field(default_factory=list)
    similar_dependency_code: List[CodeSnippet] = field(default_factory=list)

@dataclass
class GenerationRequirements:
    content: str = ""

@dataclass
class ImplementationSteps:
    content: str = ""

@dataclass
class OutputFormat:
    content: str = ""

@dataclass
class OutputRequirements:
    content: str = ""

@dataclass
class TargetTestCaseDescription:
    content: str = ""

@dataclass
class FTCorpus:
    file_path: str
    rdc_info: RdcInfo
    test_info: TestInfo
    tag_identification: TagIdentification
    code_snippets: List[CodeSnippet] = field(default_factory=list)
    similar_cases: List[Dict[str, Any]] = field(default_factory=list)
    raw_content: str = ""
    parsed_at: datetime = field(default_factory=datetime.now)

    # 新增字段
    dependency_code: List[DependencyCode] = field(default_factory=list)
    similar_code_info: Optional[SimilarCodeInfo] = None
    generation_requirements: Optional[GenerationRequirements] = None
    implementation_steps: Optional[ImplementationSteps] = None
    output_format: Optional[OutputFormat] = None
    output_requirements: Optional[OutputRequirements] = None
    target_test_case_description: Optional[TargetTestCaseDescription] = None
    
    def to_dict(self) -> Dict[str, Any]:
        result = {
            'file_path': self.file_path,
            'rdc_info': self.rdc_info.__dict__,
            'test_info': self.test_info.__dict__,
            'tag_identification': self.tag_identification.__dict__,
            'code_snippets': [snippet.__dict__ for snippet in self.code_snippets],
            'similar_cases': self.similar_cases,
            'parsed_at': self.parsed_at.isoformat(),
            'dependency_code': [dep.__dict__ for dep in self.dependency_code]
        }

        # 处理可选字段
        if self.similar_code_info:
            result['similar_code_info'] = {
                'similar_test_cases': [case.__dict__ for case in self.similar_code_info.similar_test_cases],
                'similar_ft_code': [code.__dict__ for code in self.similar_code_info.similar_ft_code],
                'similar_dependency_code': [code.__dict__ for code in self.similar_code_info.similar_dependency_code]
            }

        if self.generation_requirements:
            result['generation_requirements'] = self.generation_requirements.__dict__

        if self.implementation_steps:
            result['implementation_steps'] = self.implementation_steps.__dict__

        if self.output_format:
            result['output_format'] = self.output_format.__dict__

        if self.output_requirements:
            result['output_requirements'] = self.output_requirements.__dict__

        if self.target_test_case_description:
            result['target_test_case_description'] = self.target_test_case_description.__dict__

        return result